-- =====================================================
-- ALTERNATIVE STORAGE PERMISSIONS FIX
-- =====================================================
-- This is an alternative approach for fixing storage permissions
-- when the standard bucket_id column approach doesn't work
-- =====================================================

-- Step 1: Check what columns actually exist in storage.objects
DO $$
DECLARE
    column_exists boolean;
BEGIN
    -- Check if bucket_id column exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'storage' 
        AND table_name = 'objects' 
        AND column_name = 'bucket_id'
    ) INTO column_exists;
    
    IF column_exists THEN
        RAISE NOTICE 'bucket_id column exists in storage.objects';
    ELSE
        RAISE NOTICE 'bucket_id column does NOT exist in storage.objects';
        RAISE NOTICE 'Available columns in storage.objects:';
        
        -- Show all available columns
        FOR rec IN 
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_schema = 'storage' AND table_name = 'objects'
            ORDER BY ordinal_position
        LOOP
            RAISE NOTICE '  - %: %', rec.column_name, rec.data_type;
        END LOOP;
    END IF;
END $$;

-- Step 2: Create buckets if they don't exist
INSERT INTO storage.buckets (id, name, public) 
VALUES ('post-images', 'post-images', true)
ON CONFLICT (id) DO NOTHING;

INSERT INTO storage.buckets (id, name, public) 
VALUES ('avatars', 'avatars', true)
ON CONFLICT (id) DO NOTHING;

-- Step 3: Try the standard approach first (with bucket_id)
DO $$
DECLARE
    bucket_id_exists boolean;
BEGIN
    -- Check if bucket_id column exists
    SELECT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'storage' 
        AND table_name = 'objects' 
        AND column_name = 'bucket_id'
    ) INTO bucket_id_exists;
    
    IF bucket_id_exists THEN
        RAISE NOTICE 'Using bucket_id column for policies...';
        
        -- Drop existing policies
        DROP POLICY IF EXISTS "Anyone can view post images" ON storage.objects;
        DROP POLICY IF EXISTS "Authenticated users can upload post images" ON storage.objects;
        DROP POLICY IF EXISTS "Users can update their own post images" ON storage.objects;
        DROP POLICY IF EXISTS "Users can delete their own post images" ON storage.objects;
        DROP POLICY IF EXISTS "Anyone can view avatars" ON storage.objects;
        DROP POLICY IF EXISTS "Authenticated users can upload avatars" ON storage.objects;
        DROP POLICY IF EXISTS "Users can update their own avatars" ON storage.objects;
        DROP POLICY IF EXISTS "Users can delete their own avatars" ON storage.objects;
        
        -- Create post-images policies
        EXECUTE 'CREATE POLICY "Anyone can view post images" ON storage.objects FOR SELECT USING (bucket_id = ''post-images'')';
        EXECUTE 'CREATE POLICY "Authenticated users can upload post images" ON storage.objects FOR INSERT WITH CHECK (bucket_id = ''post-images'' AND auth.role() = ''authenticated'')';
        EXECUTE 'CREATE POLICY "Users can update their own post images" ON storage.objects FOR UPDATE USING (bucket_id = ''post-images'' AND auth.role() = ''authenticated'')';
        EXECUTE 'CREATE POLICY "Users can delete their own post images" ON storage.objects FOR DELETE USING (bucket_id = ''post-images'' AND auth.role() = ''authenticated'')';
        
        -- Create avatar policies
        EXECUTE 'CREATE POLICY "Anyone can view avatars" ON storage.objects FOR SELECT USING (bucket_id = ''avatars'')';
        EXECUTE 'CREATE POLICY "Authenticated users can upload avatars" ON storage.objects FOR INSERT WITH CHECK (bucket_id = ''avatars'' AND auth.role() = ''authenticated'')';
        EXECUTE 'CREATE POLICY "Users can update their own avatars" ON storage.objects FOR UPDATE USING (bucket_id = ''avatars'' AND auth.role() = ''authenticated'')';
        EXECUTE 'CREATE POLICY "Users can delete their own avatars" ON storage.objects FOR DELETE USING (bucket_id = ''avatars'' AND auth.role() = ''authenticated'')';
        
        RAISE NOTICE 'Storage policies created successfully using bucket_id';
    ELSE
        RAISE NOTICE 'bucket_id column not found. You may need to:';
        RAISE NOTICE '1. Update your Supabase instance';
        RAISE NOTICE '2. Enable the storage extension';
        RAISE NOTICE '3. Check if your storage schema is properly set up';
        RAISE NOTICE 'Please contact support or check your Supabase version.';
    END IF;
END $$;

-- Step 4: Verify buckets exist
SELECT 
    'Bucket verification' as status,
    id,
    name,
    public,
    created_at
FROM storage.buckets 
WHERE id IN ('post-images', 'avatars')
ORDER BY id;

-- Step 5: Show current policies
SELECT 
    'Current storage policies' as info,
    policyname,
    cmd,
    permissive
FROM pg_policies 
WHERE schemaname = 'storage' AND tablename = 'objects'
ORDER BY policyname;
